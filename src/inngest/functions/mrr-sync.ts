import { calculateMRREventsFromInvoices } from '@/lib/mrr-calculator';
import { batchUpsertMRREvents } from '@/lib/mrr-processor';
import Stripe from 'stripe';
import { inngest } from '../client';

export interface MRRSyncPayload {
  orgId: string;
}

export interface MRRSyncResult {
  orgId: string;
  totalSubscriptions: number;
  totalEventsProcessed: number;
  totalEventsCreated: number;
  totalEventsUpdated: number;
  totalErrors: number;
  successRate: number;
  duration: number;
  errors: string[];
}

const chunk = <T>(arr: T[], size: number): T[][] => {
  return arr.reduce((acc, _, i) => {
    if (i % size === 0) {
      acc.push(arr.slice(i, i + size));
    }
    return acc;
  }, [] as T[][]);
};

/**
 * Main MRR sync function that processes subscriptions in chunks of 100
 */
export const mrrSyncFunction = inngest.createFunction(
  {
    id: 'mrr-sync',
    name: 'Sync MRR Ledger from Stripe',
    concurrency: {
      limit: 5, // Limit concurrent executions to avoid overwhelming Stripe API
    },
    retries: 3,
  },
  { event: 'mrr/sync' },
  async ({ event, step, logger }) => {
    const payload = event.data as MRRSyncPayload;
    const { orgId } = payload;

    logger.info('Starting MRR sync', {
      orgId,
    });

    // const stripe = await getStripeClient(orgId);
    // if (!stripe) {
    //   throw new Error(`No Stripe integration found for org ${orgId}`);
    // }

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    // Process subscriptions in batches of 100 - one step per batch
    const batchSize = 100;
    let startingAfter: string | undefined;
    let batchIndex = 0;

    do {
      const batchResult = await step.run(
        `process-batch-${batchIndex}`,
        async () => {
          logger.info(`Processing batch ${batchIndex + 1}`, {
            batchSize,
            startingAfter,
          });

          // fetch and process the next batch of subscriptions
          const subscriptionList = await stripe.subscriptions.list({
            limit: batchSize,
            expand: ['data.items.data.price'],
            starting_after: startingAfter,
          });

          logger.info(`Fetched ${subscriptionList.data.length} subscriptions`, {
            batchSize,
            startingAfter,
          });

          const subscriptions = subscriptionList.data;

          const subscriptionInvoicesMap = await createSubscriptionsInvoicesMap(
            stripe,
            subscriptions
          );

          logger.info(
            `Fetched invoices for ${subscriptions.length} subscriptions`,
            {
              batchSize,
              startingAfter,
            }
          );

          const mrrEvents = subscriptions.map(sub => {
            const invoices = subscriptionInvoicesMap.get(sub.id) || [];
            return calculateMRREventsFromInvoices(sub, invoices);
          });

          logger.info(`Calculated ${mrrEvents.flat().length} MRR events`, {
            batchSize,
            startingAfter,
          });

          await batchUpsertMRREvents(mrrEvents.flat());

          logger.info(`Upserted ${mrrEvents.flat().length} MRR events`, {
            batchSize,
            startingAfter,
          });

          return {
            hasMore: subscriptionList.has_more,
            startingAfter:
              subscriptionList.data[subscriptionList.data.length - 1].id,
          };
        }
      );

      startingAfter = batchResult.startingAfter;
      batchIndex++;
    } while (startingAfter); // Continue until no more subscriptions
  }
);

/**
 * Fetch all invoices for the given subscriptions using Stripe invoices.search
 * in groups of 10 subscription IDs combined with OR, and return a map
 * subscriptionId -> invoices[] (sorted oldest first).
 */
async function createSubscriptionsInvoicesMap(
  stripe: Stripe,
  subscriptions: Stripe.Subscription[]
): Promise<Map<string, Stripe.Invoice[]>> {
  const map = new Map<string, Stripe.Invoice[]>();

  const ids = subscriptions.map(s => s.id).filter(Boolean);
  // Initialize map with empty arrays to ensure all subs are present
  for (const id of ids) map.set(id, []);

  // Process in groups of 10 per Stripe search query
  for (const group of chunk(ids, 10)) {
    if (group.length === 0) continue;
    const query = group.map(id => `subscription:"${id}"`).join(' OR ');

    let page: string | undefined;
    do {
      const res = await stripe.invoices.search({
        query,
        limit: 100,
        page,
        // Expand useful fields we commonly rely on downstream
        expand: ['data.customer', 'data.lines.data.price.product'],
      });

      for (const inv of res.data) {
        const subId = (inv as any).subscription?.id;
        if (!subId) continue;
        if (!map.has(subId)) map.set(subId, []);
        map.get(subId)!.push(inv);
      }

      page = res.next_page || undefined;
    } while (page);
  }

  // Sort each subscription's invoices from oldest to newest
  for (const invoices of map.values()) {
    invoices.sort((a, b) => a.created - b.created);
  }

  return map;
}

/**
 * Process and save a batch of subscriptions - handles everything in one step:
 * 1. Fetch next batch of subscriptions (up to batchSize)
 * 2. Fetch all their invoices using Stripe search
 * 3. Calculate MRR events from invoices
 * 4. Batch upsert all MRR events to database
 * 5. Return cursor for next batch
 */
