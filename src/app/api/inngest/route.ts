// this is ingest route created using ingest serve

import { serve } from 'inngest/next';
import { inngest } from '../../../ingest/client';
import { populateStripeTestData } from '../../../ingest/stripe/populate-test-data';
import { mrrSyncClockedData } from '../../../ingest/stripe/sync-clock-data';
import { mrrSyncFunction } from '../../../inngest/functions/mrr-sync';

// // Create an API that serves zero functions
export const { GET, POST, PUT } = serve({
  client: inngest,
  functions: [populateStripeTestData, mrrSyncFunction, mrrSyncClockedData],
});

// export const dynamic = 'force-dynamic';
