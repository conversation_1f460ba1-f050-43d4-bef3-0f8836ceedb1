import { calculateMRREventsFromInvoices } from '@/lib/mrr-calculator';
import { batchUpsertMRREvents } from '@/lib/mrr-processor';
import Stripe from 'stripe';
import { inngest } from '../../inngest/client';

export interface MRRSyncPayload {
  orgId: string;
}

export const mrrSyncClockedData = inngest.createFunction(
  {
    id: 'mrr-sync-test',
    name: 'Sync MRR Ledger from Stripe',
    concurrency: {
      limit: 5, // Limit concurrent executions to avoid overwhelming Stripe API
    },
  },
  { event: 'mrr/sync-test' },
  async ({ event, step, logger }) => {
    const payload = event.data as MRRSyncPayload;
    const { orgId } = payload;

    logger.info('Starting MRR sync', {
      orgId,
    });

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    // Fetch all test clocks with pagination
    let allTestClocks: Stripe.TestHelpers.TestClock[] = [];
    let hasMoreClocks = true;
    let startingAfterClock: string | undefined;

    logger.info('Fetching all test clocks...');

    while (hasMoreClocks) {
      const testClocksResponse = await stripe.testHelpers.testClocks.list({
        limit: 100,
        starting_after: startingAfterClock,
      });

      allTestClocks.push(...testClocksResponse.data);
      hasMoreClocks = testClocksResponse.has_more;

      if (hasMoreClocks && testClocksResponse.data.length > 0) {
        startingAfterClock =
          testClocksResponse.data[testClocksResponse.data.length - 1].id;
      }
    }

    logger.info(`Found ${allTestClocks.length} test clocks to process`);

    // Process each test clock in batches using Inngest steps
    let clockIndex = 0;
    for (const testClock of allTestClocks) {
      await step.run(`process-clock-${clockIndex}`, async () => {
        logger.info(
          `Processing test clock ${clockIndex + 1}/${allTestClocks.length}`,
          {
            testClockId: testClock.id,
            testClockName: testClock.name,
          }
        );

        // Fetch all subscriptions for this test clock
        let allSubscriptions: Stripe.Subscription[] = [];
        let hasMoreSubscriptions = true;
        let startingAfterSubscription: string | undefined;

        while (hasMoreSubscriptions) {
          const subscriptionsResponse = await stripe.subscriptions.list({
            test_clock: testClock.id,
            limit: 100,
            expand: ['data.items.data.price'],
            starting_after: startingAfterSubscription,
          });

          allSubscriptions.push(...subscriptionsResponse.data);
          hasMoreSubscriptions = subscriptionsResponse.has_more;

          if (hasMoreSubscriptions && subscriptionsResponse.data.length > 0) {
            startingAfterSubscription =
              subscriptionsResponse.data[subscriptionsResponse.data.length - 1]
                .id;
          }
        }

        logger.info(
          `Found ${allSubscriptions.length} subscriptions for test clock ${testClock.id}`
        );

        if (allSubscriptions.length === 0) {
          return { processedSubscriptions: 0, processedEvents: 0 };
        }

        // Create subscriptions-to-invoices map
        const subscriptionInvoicesMap = await createSubscriptionsInvoicesMap(
          stripe,
          allSubscriptions
        );

        logger.info(
          `Fetched invoices for ${allSubscriptions.length} subscriptions`
        );

        // Calculate MRR events for all subscriptions
        const mrrEvents = allSubscriptions.map(sub => {
          const invoices = subscriptionInvoicesMap.get(sub.id) || [];
          return calculateMRREventsFromInvoices(sub, invoices);
        });

        const flatMrrEvents = mrrEvents.flat();
        logger.info(
          `Calculated ${flatMrrEvents.length} MRR events for test clock ${testClock.id}`
        );

        // Batch upsert all MRR events
        await batchUpsertMRREvents(flatMrrEvents);

        logger.info(
          `Upserted ${flatMrrEvents.length} MRR events for test clock ${testClock.id}`
        );

        return {
          processedSubscriptions: allSubscriptions.length,
          processedEvents: flatMrrEvents.length,
        };
      });

      clockIndex++;
    }

    logger.info(`Completed processing ${allTestClocks.length} test clocks`);
  }
);

/**
 * Process and save a batch of subscriptions - handles everything in one step:
 * 1. Fetch next batch of subscriptions (up to batchSize)
 * 2. Fetch all their invoices using Stripe search
 * 3. Calculate MRR events from invoices
 * 4. Batch upsert all MRR events to database
 * 5. Return cursor for next batch
 */
