import { calculateMRREventsFromInvoices } from '@/lib/mrr-calculator';
import { batchUpsertMRREvents } from '@/lib/mrr-processor';
import Stripe from 'stripe';
import { inngest } from '../../inngest/client';

export interface MRRSyncPayload {
  orgId: string;
}

export const mrrSyncClockedData = inngest.createFunction(
  {
    id: 'mrr-sync-test',
    name: 'Sync MRR Ledger from Stripe',
    concurrency: {
      limit: 5, // Limit concurrent executions to avoid overwhelming Stripe API
    },
  },
  { event: 'mrr/sync-test' },
  async ({ event, step, logger }) => {
    const payload = event.data as MRRSyncPayload;
    const { orgId } = payload;

    logger.info('Starting MRR sync', {
      orgId,
    });

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    // fetch all the test clocks, there can be multiple pages
    const testClocks = await stripe.testHelpers.testClocks.list({
      limit: 100,
    });

    const hasMoreClocks = testClocks.has_more;
    let startingAfter = testClocks.data[testClocks.data.length - 1].id;
    let clockIndex = 0;
    do {
      const batchResult = await step.run(

);

/**
 * Fetch all invoices for the given subscriptions using Stripe invoices.search
 * in groups of 10 subscription IDs combined with OR, and return a map
 * subscriptionId -> invoices[] (sorted oldest first).
 */
async function createSubscriptionsInvoicesMap(
  stripe: Stripe,
  subscriptions: Stripe.Subscription[]
): Promise<Map<string, Stripe.Invoice[]>> {
  const map = new Map<string, Stripe.Invoice[]>();

  const ids = subscriptions.map(s => s.id).filter(Boolean);
  // Initialize map with empty arrays to ensure all subs are present
  for (const id of ids) map.set(id, []);

  // Process in groups of 10 per Stripe search query
  for (const group of chunk(ids, 10)) {
    if (group.length === 0) continue;
    const query = group.map(id => `subscription:"${id}"`).join(' OR ');

    let page: string | undefined;
    do {
      const res = await stripe.invoices.search({
        query,
        limit: 100,
        page,
        // Expand useful fields we commonly rely on downstream
        expand: ['data.customer', 'data.lines.data.price.product'],
      });

      for (const inv of res.data) {
        const subId = (inv as any).subscription?.id;
        if (!subId) continue;
        if (!map.has(subId)) map.set(subId, []);
        map.get(subId)!.push(inv);
      }

      page = res.next_page || undefined;
    } while (page);
  }

  // Sort each subscription's invoices from oldest to newest
  for (const invoices of map.values()) {
    invoices.sort((a, b) => a.created - b.created);
  }

  return map;
}

/**
 * Process and save a batch of subscriptions - handles everything in one step:
 * 1. Fetch next batch of subscriptions (up to batchSize)
 * 2. Fetch all their invoices using Stripe search
 * 3. Calculate MRR events from invoices
 * 4. Batch upsert all MRR events to database
 * 5. Return cursor for next batch
 */
